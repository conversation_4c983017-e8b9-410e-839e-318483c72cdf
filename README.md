# Microdrama Scraper

A Python web scraper for extracting microdrama data from juben.pro and storing it in a SQLite database.

## Features

- Scrapes microdrama information including title, genre, episodes, synopsis, etc.
- Handles login authentication using browser cookies
- Stores data in SQLite database with proper schema
- Supports pagination to scrape multiple pages
- Includes logging and error handling
- Respects website rate limits

## Installation

1. Clone or download the project
2. Install dependencies:
```bash
pip install -r requirements.txt
```

## Usage

### Basic Usage
```bash
python main.py
```

### With Custom URL
```bash
python main.py --url "https://www.juben.pro/MicroDrama/"
```

### View Database Statistics
```bash
python main.py --stats
```

### Verbose Logging
```bash
python main.py --verbose
```

## Database Schema

The scraper creates two tables:

### microdramas
- id (Primary Key)
- title
- genre
- episode_count
- episode_length
- release_date
- synopsis
- screenwriter
- vip_status
- thumbnail_url
- detail_url
- scraped_at

### scraping_logs
- id (Primary Key)
- page_url
- items_scraped
- success
- error_message
- scraped_at

## Configuration

Edit `config.py` to modify:
- Request delays
- Browser settings
- Database path
- User agent strings

## Login Handling

If the website requires login:
1. The scraper will detect login requirement
2. It will prompt you to login manually in the browser
3. Cookies will be saved for future sessions

## Files

- `main.py` - Main application entry point
- `scraper.py` - Core scraping logic
- `database.py` - Database operations
- `config.py` - Configuration settings
- `requirements.txt` - Python dependencies

## Notes

- The scraper is designed to be respectful of the website
- It includes delays between requests to avoid overwhelming the server
- Login cookies are cached to avoid repeated login prompts
- All scraped data is stored locally in SQLite database