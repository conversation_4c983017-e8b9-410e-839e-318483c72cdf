import sqlite3
import logging
from datetime import datetime
from config import DB_PATH

class DatabaseManager:
    def __init__(self):
        self.db_path = DB_PATH
        self.init_database()
    
    def init_database(self):
        """Initialize the SQLite database with required tables"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Create microdramas table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS microdramas (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    genre TEXT,
                    episode_count INTEGER,
                    episode_length TEXT,
                    release_date TEXT,
                    synopsis TEXT,
                    screenwriter TEXT,
                    vip_status TEXT,
                    thumbnail_url TEXT,
                    detail_url TEXT,
                    full_content TEXT,
                    price TEXT,
                    contact_info TEXT,
                    detail_scraped BOOLEAN DEFAULT FALSE,
                    scraped_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(title, screenwriter)
                )
            """)
            
            # Create scraping_logs table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS scraping_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    page_url TEXT,
                    items_scraped INTEGER,
                    success BOOLEAN,
                    error_message TEXT,
                    scraped_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.commit()
            logging.info("Database initialized successfully")
    
    def insert_microdrama(self, data):
        """Insert a microdrama record into the database"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            try:
                cursor.execute("""
                    INSERT OR REPLACE INTO microdramas 
                    (title, genre, episode_count, episode_length, release_date, 
                     synopsis, screenwriter, vip_status, thumbnail_url, detail_url,
                     full_content, price, contact_info, detail_scraped)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    data.get('title'),
                    data.get('genre'),
                    data.get('episode_count'),
                    data.get('episode_length'),
                    data.get('release_date'),
                    data.get('synopsis'),
                    data.get('screenwriter'),
                    data.get('vip_status'),
                    data.get('thumbnail_url'),
                    data.get('detail_url'),
                    data.get('full_content'),
                    data.get('price'),
                    data.get('contact_info'),
                    data.get('detail_scraped', False)
                ))
                
                conn.commit()
                return True
            except sqlite3.Error as e:
                logging.error(f"Error inserting microdrama: {e}")
                return False
    
    def log_scraping_session(self, page_url, items_scraped, success=True, error_message=None):
        """Log a scraping session"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO scraping_logs 
                (page_url, items_scraped, success, error_message)
                VALUES (?, ?, ?, ?)
            """, (page_url, items_scraped, success, error_message))
            
            conn.commit()
    
    def get_total_records(self):
        """Get total number of microdrama records"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM microdramas")
            return cursor.fetchone()[0]
    
    def get_records_by_genre(self):
        """Get count of records by genre"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT genre, COUNT(*) as count 
                FROM microdramas 
                GROUP BY genre 
                ORDER BY count DESC
            """)
            return cursor.fetchall()
    
    def get_records_without_details(self):
        """Get records that don't have detailed content scraped"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT id, title, detail_url 
                FROM microdramas 
                WHERE detail_scraped = FALSE AND detail_url IS NOT NULL
                ORDER BY id
            """)
            return cursor.fetchall()
    
    def update_detail_content(self, record_id, detail_data):
        """Update a record with detailed content"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            try:
                cursor.execute("""
                    UPDATE microdramas 
                    SET full_content = ?, price = ?, contact_info = ?, 
                        detail_scraped = TRUE, synopsis = COALESCE(?, synopsis)
                    WHERE id = ?
                """, (
                    detail_data.get('full_content'),
                    detail_data.get('price'),
                    detail_data.get('contact_info'),
                    detail_data.get('synopsis'),
                    record_id
                ))
                
                conn.commit()
                return True
            except sqlite3.Error as e:
                logging.error(f"Error updating detail content: {e}")
                return False