import os
from dotenv import load_dotenv

load_dotenv()

# Website configuration
BASE_URL = "https://www.juben.pro/MicroDrama/"
DELAY_BETWEEN_REQUESTS = 1  # seconds
MAX_RETRIES = 3

# Database configuration
DB_PATH = "microdrama_data.db"

# Browser configuration
HEADLESS = True
BROWSER_TIMEOUT = 30

# Cookie/session configuration
COOKIE_FILE = "cookies.json"

# Headers
USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"