import time
import json
import logging
import requests
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from fake_useragent import UserAgent
from database import DatabaseManager
from config import *

class MicrodramaScraper:
    def __init__(self):
        self.db = DatabaseManager()
        self.ua = UserAgent()
        self.driver = None
        self.session = requests.Session()
        self.setup_logging()
    
    def setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('scraper.log'),
                logging.StreamHandler()
            ]
        )
    
    def setup_driver(self):
        """Setup Chrome WebDriver with options"""
        chrome_options = Options()
        if HEADLESS:
            chrome_options.add_argument("--headless=new")
        
        chrome_options.add_argument(f"--user-agent={USER_AGENT}")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-plugins")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--allow-running-insecure-content")
        
        # Use system chromedriver
        service = Service('/opt/homebrew/bin/chromedriver')
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        self.driver.implicitly_wait(10)
    
    def load_cookies(self):
        """Load cookies from file if available"""
        try:
            with open(COOKIE_FILE, 'r') as f:
                cookies = json.load(f)
                for cookie in cookies:
                    self.driver.add_cookie(cookie)
            logging.info("Cookies loaded successfully")
        except FileNotFoundError:
            logging.info("No cookie file found, proceeding without cookies")
    
    def save_cookies(self):
        """Save current cookies to file"""
        try:
            cookies = self.driver.get_cookies()
            with open(COOKIE_FILE, 'w') as f:
                json.dump(cookies, f)
            logging.info("Cookies saved successfully")
        except Exception as e:
            logging.error(f"Error saving cookies: {e}")
    
    def login_check(self):
        """Check if already logged in, handle login if needed"""
        try:
            # Check for login indicators
            login_elements = self.driver.find_elements(By.CLASS_NAME, "login")
            if login_elements:
                logging.warning("Login required. Please login manually in the browser.")
                input("Press Enter after logging in manually...")
                self.save_cookies()
            else:
                logging.info("Already logged in or no login required")
        except Exception as e:
            logging.error(f"Error checking login status: {e}")
    
    def extract_microdrama_data(self, element):
        """Extract data from a microdrama element"""
        try:
            data = {}
            
            # Extract all text content first
            all_text = element.get_text(strip=True)
            
            # Extract title - try various selectors
            title_elem = (element.find('h3') or element.find('h4') or 
                         element.find(class_=lambda x: x and 'title' in x.lower() if x else False) or
                         element.find('a'))
            
            if title_elem:
                data['title'] = title_elem.get_text(strip=True)
            else:
                # Fallback - use first line of text
                lines = all_text.split('\n')
                data['title'] = lines[0] if lines else "Unknown"
            
            # Extract genre - look for common genre keywords
            genre_patterns = ['喜剧', '爱情', '都市', '农村', '古装', '悬疑', '科幻', '动作', '剧情']
            data['genre'] = "Unknown"
            for pattern in genre_patterns:
                if pattern in all_text:
                    data['genre'] = pattern
                    break
            
            # Extract episode info
            import re
            episode_match = re.search(r'(\d+)集', all_text)
            data['episode_count'] = int(episode_match.group(1)) if episode_match else None
            
            # Extract length info
            length_match = re.search(r'(\d+)分钟', all_text)
            data['episode_length'] = f"{length_match.group(1)}分钟" if length_match else "Unknown"
            
            # Extract release date
            date_match = re.search(r'(\d{4}-\d{2}-\d{2})', all_text)
            data['release_date'] = date_match.group(1) if date_match else "Unknown"
            
            # Extract synopsis - use remaining text
            data['synopsis'] = all_text[:200] + "..." if len(all_text) > 200 else all_text
            
            # Extract screenwriter
            writer_match = re.search(r'编剧[：:]\s*([^，,\n]+)', all_text)
            data['screenwriter'] = writer_match.group(1).strip() if writer_match else "Unknown"
            
            # Extract VIP status
            data['vip_status'] = "VIP" if 'VIP' in all_text or '会员' in all_text else "Free"
            
            # Extract thumbnail URL
            img_elem = element.find('img')
            data['thumbnail_url'] = img_elem.get('src') if img_elem else None
            
            # Extract detail URL
            link_elem = element.find('a')
            if link_elem:
                href = link_elem.get('href')
                if href:
                    # Make sure URL is absolute
                    if href.startswith('/'):
                        data['detail_url'] = f"https://www.juben.pro{href}"
                    elif href.startswith('http'):
                        data['detail_url'] = href
                    else:
                        data['detail_url'] = f"https://www.juben.pro/{href}"
                else:
                    data['detail_url'] = None
            else:
                data['detail_url'] = None
            
            return data
            
        except Exception as e:
            logging.error(f"Error extracting microdrama data: {e}")
            return None
    
    def extract_number(self, text):
        """Extract number from text"""
        import re
        numbers = re.findall(r'\d+', text)
        return int(numbers[0]) if numbers else None
    
    def scrape_page(self, url):
        """Scrape a single page of microdramas"""
        try:
            self.driver.get(url)
            time.sleep(DELAY_BETWEEN_REQUESTS)
            
            # Wait for page to load
            WebDriverWait(self.driver, BROWSER_TIMEOUT).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Get page source and parse with BeautifulSoup
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            # Debug: print page title
            page_title = soup.find('title')
            logging.info(f"Page title: {page_title.text if page_title else 'No title'}")
            
            # Try multiple selectors to find drama elements
            drama_elements = []
            
            # Try common selectors
            selectors = [
                'div[class*="item"]',
                'div[class*="card"]', 
                'div[class*="drama"]',
                'article',
                'li[class*="item"]',
                '.list-item',
                '.script-item',
                '.drama-item'
            ]
            
            for selector in selectors:
                elements = soup.select(selector)
                if elements:
                    drama_elements = elements
                    logging.info(f"Found {len(elements)} elements with selector: {selector}")
                    break
            
            # If no specific elements found, try to find any divs with text content
            if not drama_elements:
                all_divs = soup.find_all('div')
                # Filter divs that contain substantial text
                drama_elements = [div for div in all_divs if div.get_text(strip=True) and len(div.get_text(strip=True)) > 20]
                logging.info(f"Found {len(drama_elements)} divs with substantial text")
            
            scraped_count = 0
            for element in drama_elements[:50]:  # Limit to first 50 elements
                data = self.extract_microdrama_data(element)
                if data and data['title'] != "Unknown" and len(data['title']) > 2:
                    if self.db.insert_microdrama(data):
                        scraped_count += 1
                        logging.info(f"Scraped: {data['title']}")
                    
                    # Print first few items for debugging
                    if scraped_count <= 3:
                        logging.info(f"Sample data: {data}")
            
            self.db.log_scraping_session(url, scraped_count, success=True)
            return scraped_count
            
        except Exception as e:
            logging.error(f"Error scraping page {url}: {e}")
            self.db.log_scraping_session(url, 0, success=False, error_message=str(e))
            return 0
    
    def find_next_page(self):
        """Find and return the next page URL"""
        try:
            next_buttons = self.driver.find_elements(By.XPATH, "//a[contains(text(), '下一页') or contains(text(), 'Next') or contains(@class, 'next')]")
            if next_buttons:
                return next_buttons[0].get_attribute('href')
            return None
        except Exception as e:
            logging.error(f"Error finding next page: {e}")
            return None
    
    def scrape_all_pages(self, start_url=None):
        """Scrape all pages starting from the given URL"""
        if not start_url:
            start_url = BASE_URL
        
        self.setup_driver()
        
        try:
            # Load initial page
            self.driver.get(start_url)
            time.sleep(2)
            
            # Load cookies and check login
            self.load_cookies()
            self.login_check()
            
            current_url = start_url
            page_count = 0
            total_scraped = 0
            
            while current_url and page_count < 100:  # Limit to prevent infinite loop
                page_count += 1
                logging.info(f"Scraping page {page_count}: {current_url}")
                
                scraped_count = self.scrape_page(current_url)
                total_scraped += scraped_count
                
                # Find next page
                next_url = self.find_next_page()
                if not next_url:
                    logging.info("No more pages found")
                    break
                
                current_url = next_url
                time.sleep(DELAY_BETWEEN_REQUESTS)
            
            logging.info(f"Scraping completed. Total pages: {page_count}, Total items: {total_scraped}")
            
        except Exception as e:
            logging.error(f"Error during scraping: {e}")
        finally:
            if self.driver:
                self.driver.quit()
    
    def scrape_with_filters(self, genres=None, vip_only=False):
        """Scrape with specific filters"""
        # This method would need to be customized based on the actual website's filter system
        pass
    
    def scrape_detail_page(self, detail_url):
        """Scrape detailed content from a single detail page"""
        try:
            self.driver.get(detail_url)
            time.sleep(DELAY_BETWEEN_REQUESTS)
            
            # Wait for page to load
            WebDriverWait(self.driver, BROWSER_TIMEOUT).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Get page source and parse with BeautifulSoup
            soup = BeautifulSoup(self.driver.page_source, 'html.parser')
            
            detail_data = {}
            
            # Extract full content - try to find the main content area
            content_selectors = [
                '.article-content',
                '.content',
                '.main-content',
                '.script-content',
                '.post-content',
                '#content',
                'article',
                '.entry-content'
            ]
            
            full_content = ""
            for selector in content_selectors:
                content_elem = soup.select_one(selector)
                if content_elem:
                    full_content = content_elem.get_text(strip=True)
                    break
            
            # If no specific content found, extract from body
            if not full_content:
                body = soup.find('body')
                if body:
                    # Remove navigation and footer elements
                    for unwanted in body.find_all(['nav', 'header', 'footer', 'aside']):
                        unwanted.decompose()
                    full_content = body.get_text(strip=True)
            
            detail_data['full_content'] = full_content
            
            # Extract price information
            price_patterns = [
                r'价格[：:]\s*([^\n，,]+)',
                r'售价[：:]\s*([^\n，,]+)',
                r'报价[：:]\s*([^\n，,]+)',
                r'(\d+元)',
                r'(\d+\.\d+元)',
                r'免费',
                r'VIP'
            ]
            
            price_info = ""
            for pattern in price_patterns:
                import re
                match = re.search(pattern, full_content)
                if match:
                    price_info = match.group(1) if match.lastindex else match.group(0)
                    break
            
            detail_data['price'] = price_info
            
            # Extract contact information
            contact_patterns = [
                r'联系[：:]\s*([^\n，,]+)',
                r'QQ[：:]\s*(\d+)',
                r'微信[：:]\s*([^\n，,]+)',
                r'手机[：:]\s*([^\n，,]+)',
                r'电话[：:]\s*([^\n，,]+)',
                r'邮箱[：:]\s*([^\n，,]+)'
            ]
            
            contact_info = ""
            for pattern in contact_patterns:
                match = re.search(pattern, full_content)
                if match:
                    contact_info += match.group(1) + "; "
            
            detail_data['contact_info'] = contact_info.strip("; ")
            
            # Extract better synopsis from detail page
            synopsis_selectors = [
                '.synopsis',
                '.summary',
                '.description',
                '.intro',
                '.abstract'
            ]
            
            synopsis = ""
            for selector in synopsis_selectors:
                synopsis_elem = soup.select_one(selector)
                if synopsis_elem:
                    synopsis = synopsis_elem.get_text(strip=True)
                    break
            
            if synopsis:
                detail_data['synopsis'] = synopsis
            
            return detail_data
            
        except Exception as e:
            logging.error(f"Error scraping detail page {detail_url}: {e}")
            return None
    
    def scrape_all_details(self):
        """Scrape detailed content for all records without details"""
        records = self.db.get_records_without_details()
        
        if not records:
            logging.info("No records found without details")
            return
        
        self.setup_driver()
        
        try:
            self.driver.get("https://www.juben.pro/")
            time.sleep(2)
            self.load_cookies()
            self.login_check()
            
            total_records = len(records)
            logging.info(f"Found {total_records} records without details")
            
            for i, (record_id, title, detail_url) in enumerate(records, 1):
                logging.info(f"Scraping details for {i}/{total_records}: {title}")
                
                detail_data = self.scrape_detail_page(detail_url)
                if detail_data:
                    if self.db.update_detail_content(record_id, detail_data):
                        logging.info(f"Updated details for: {title}")
                    else:
                        logging.error(f"Failed to update details for: {title}")
                else:
                    logging.error(f"Failed to scrape details for: {title}")
                
                time.sleep(DELAY_BETWEEN_REQUESTS)
                
        except Exception as e:
            logging.error(f"Error during detail scraping: {e}")
        finally:
            if self.driver:
                self.driver.quit()
    
    def close(self):
        """Clean up resources"""
        if self.driver:
            self.driver.quit()