#!/usr/bin/env python3
"""
Microdrama Scraper Main Application
Scrapes microdrama data from juben.pro and stores in SQLite database
"""

import argparse
import logging
from scraper import MicrodramaScraper
from database import DatabaseManager
from config import BASE_URL

def main():
    parser = argparse.ArgumentParser(description='Scrape microdrama data from juben.pro')
    parser.add_argument('--url', default=BASE_URL, help='Starting URL to scrape')
    parser.add_argument('--pages', type=int, default=None, help='Maximum number of pages to scrape')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose logging')
    parser.add_argument('--stats', action='store_true', help='Show database statistics')
    parser.add_argument('--details', action='store_true', help='Scrape detailed content for existing records')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Initialize database
    db = DatabaseManager()
    
    if args.stats:
        # Show statistics
        total_records = db.get_total_records()
        genre_stats = db.get_records_by_genre()
        
        print(f"\nDatabase Statistics:")
        print(f"Total Records: {total_records}")
        print(f"\nRecords by Genre:")
        for genre, count in genre_stats:
            print(f"  {genre}: {count}")
        return
    
    # Initialize scraper
    scraper = MicrodramaScraper()
    
    try:
        if args.details:
            # Scrape detailed content for existing records
            print("Starting detailed content scraping for existing records...")
            scraper.scrape_all_details()
            
            # Show final statistics
            total_records = db.get_total_records()
            records_without_details = db.get_records_without_details()
            print(f"\nDetail scraping completed!")
            print(f"Total records in database: {total_records}")
            print(f"Records still without details: {len(records_without_details)}")
        else:
            # Regular list scraping
            print(f"Starting scraping from: {args.url}")
            print("Note: If login is required, you'll be prompted to login manually")
            print("The scraper will automatically handle pagination and data extraction")
            
            # Start scraping
            scraper.scrape_all_pages(args.url)
            
            # Show final statistics
            total_records = db.get_total_records()
            print(f"\nScraping completed! Total records in database: {total_records}")
            
            # Ask if user wants to scrape details
            records_without_details = db.get_records_without_details()
            if records_without_details:
                print(f"\nFound {len(records_without_details)} records without detailed content.")
                print("Run with --details flag to scrape detailed content for these records.")
        
    except KeyboardInterrupt:
        print("\nScraping interrupted by user")
    except Exception as e:
        print(f"Error during scraping: {e}")
        logging.error(f"Error during scraping: {e}")
    finally:
        scraper.close()

if __name__ == "__main__":
    main()